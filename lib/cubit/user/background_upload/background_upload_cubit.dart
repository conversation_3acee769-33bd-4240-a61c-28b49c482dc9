import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_social/core/repository/social_repository.dart';

part 'background_upload_state.dart';

class BackgroundUploadCubit extends Cubit<BackgroundUploadState> {
  BackgroundUploadCubit() : super(const BackgroundUploadState());

  final ImagePicker _imagePicker = ImagePicker();
  final ImageCropper _imageCropper = ImageCropper();
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  // Callback function to be called when upload is successful
  Function(String mediaKey)? onUploadSuccess;

  /// Pick image from gallery with cropping
  Future<void> pickImageFromGallery() async {
    try {
      emit(state.copyWith(status: BackgroundUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        // Crop the image with background-specific settings
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          // Update local image path with cropped image
          emit(
            state.copyWith(
              localImagePath: croppedFile.path,
              errorMessage: null,
            ),
          );

          // Automatically upload the cropped image
          await uploadBackground();
        } else {
          emit(
            state.copyWith(
              status: BackgroundUploadStatus.initial,
              errorMessage: null,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'Error selecting image: ${e.toString()}',
        ),
      );
    }
  }

  /// Pick image from camera with cropping
  Future<void> pickImageFromCamera() async {
    try {
      emit(state.copyWith(status: BackgroundUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        // Crop the image with background-specific settings
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          // Update local image path with cropped image
          emit(
            state.copyWith(
              localImagePath: croppedFile.path,
              errorMessage: null,
            ),
          );

          // Automatically upload the cropped image
          await uploadBackground();
        } else {
          emit(
            state.copyWith(
              status: BackgroundUploadStatus.initial,
              errorMessage: null,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'Error taking photo: ${e.toString()}',
        ),
      );
    }
  }

  /// Crop image with background-specific settings (4:3 aspect ratio for better mobile backgrounds)
  Future<CroppedFile?> _cropImage(String imagePath) async {
    try {
      final croppedFile = await _imageCropper.cropImage(
        sourcePath: imagePath,
        uiSettings: [
          AndroidUiSettings(
            toolbarColor: const Color(0xFF1A1A1A),
            toolbarWidgetColor: const Color(0xFFFFFFFF),
            backgroundColor: const Color(0xFF000000),
            activeControlsWidgetColor: const Color(0xFF007AFF),
            cropFrameColor: const Color(0xFF007AFF),
            cropGridColor: const Color(0x80FFFFFF),
            initAspectRatio: CropAspectRatioPreset.ratio4x3,
            lockAspectRatio: false, // Allow users to choose different ratios
            hideBottomControls: false,
            showCropGrid: true,
            aspectRatioPresets: [
              CropAspectRatioPreset
                  .ratio4x3, // Classic photo ratio - great for backgrounds
              CropAspectRatioPreset
                  .ratio3x2, // Photography standard - elegant ratio
              CropAspectRatioPreset.square, // Square - modern and clean
              CropAspectRatioPreset
                  .ratio16x9, // Widescreen - for landscape backgrounds
              CropAspectRatioPreset.original, // Keep original proportions
            ],
          ),
          IOSUiSettings(
            doneButtonTitle: 'Done',
            cancelButtonTitle: 'Cancel',
            aspectRatioPickerButtonHidden: false,
            resetButtonHidden: false,
            rotateButtonsHidden: false,
            aspectRatioLockEnabled: false, // Allow ratio switching on iOS
            resetAspectRatioEnabled: true,
            rotateClockwiseButtonHidden: false,
            hidesNavigationBar: false,
            aspectRatioPresets: [
              CropAspectRatioPreset.ratio4x3, // Classic photo ratio
              CropAspectRatioPreset.ratio3x2, // Photography standard
              CropAspectRatioPreset.square, // Square format
              CropAspectRatioPreset.ratio16x9, // Widescreen
              CropAspectRatioPreset.original, // Original proportions
            ],
          ),
        ],
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        maxWidth: 1920,
        maxHeight: 1440, // Updated for 4:3 ratio (1920x1440)
      );
      return croppedFile;
    } catch (e) {
      // Log error cropping background image
      return null;
    }
  }

  /// Upload background image to server
  Future<void> uploadBackground() async {
    if (state.localImagePath == null) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'No image selected',
        ),
      );
      return;
    }

    try {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.loading,
          uploadProgress: 0.0,
        ),
      );

      final file = File(state.localImagePath!);

      // Upload using social repository
      var result = await _socialRepository.uploadMedia(file, 'image');

      // Debug logging
      print('BackgroundUpload: Upload result - key: ${result.data.key}');

      // Check if upload was successful and we have a media key
      if (result.data.key != null) {
        print('BackgroundUpload: Upload successful, emitting success state');
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.success,
            mediaKey: result.data.key,
            uploadedImageUrl:
                state.localImagePath!, // Temporary until we get actual URL
            uploadProgress: 1.0,
            errorMessage: null,
          ),
        );

        // Call callback if provided
        if (onUploadSuccess != null) {
          print('BackgroundUpload: Calling onUploadSuccess callback');
          onUploadSuccess!(result.data.key!);
        }
      } else {
        print('BackgroundUpload: Upload failed - no media key received');
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.failure,
            errorMessage: 'Upload failed: No media key received',
            uploadProgress: 0.0,
          ),
        );
      }
    } catch (e) {
      final errorMessage = 'Failed to upload background: ${e.toString()}';

      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: errorMessage,
          uploadProgress: 0.0,
        ),
      );
    }
  }

  /// Clear the current background selection
  void clearBackground() {
    emit(const BackgroundUploadState());
  }

  /// Reset state to initial
  void resetState() {
    emit(const BackgroundUploadState());
  }
}
